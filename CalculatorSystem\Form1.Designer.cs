namespace CalculatorSystem
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        private System.Windows.Forms.GroupBox groupBoxFile;
        private System.Windows.Forms.Button btnSelectFile;
        private System.Windows.Forms.TextBox txtFilePath;
        private System.Windows.Forms.Label lblFilePath;
        private System.Windows.Forms.GroupBox groupBoxSettings;
        private System.Windows.Forms.NumericUpDown numMaxNumber;
        private System.Windows.Forms.Label lblMaxNumber;
        private System.Windows.Forms.NumericUpDown numMagneticPoints;
        private System.Windows.Forms.Label lblMagneticPoints;
        private System.Windows.Forms.NumericUpDown numTwoCombinationMax;
        private System.Windows.Forms.NumericUpDown numTwoCombinationMin;
        private System.Windows.Forms.Label lblTwoCombination;
        private System.Windows.Forms.NumericUpDown numResultMax;
        private System.Windows.Forms.NumericUpDown numResultMin;
        private System.Windows.Forms.Label lblResultRange;
        private System.Windows.Forms.GroupBox groupBoxProcess;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.GroupBox groupBoxResults;
        private System.Windows.Forms.TextBox txtResults;
        private System.Windows.Forms.Label lblResultCount;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxFile = new System.Windows.Forms.GroupBox();
            this.btnSelectFile = new System.Windows.Forms.Button();
            this.txtFilePath = new System.Windows.Forms.TextBox();
            this.lblFilePath = new System.Windows.Forms.Label();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.numMaxNumber = new System.Windows.Forms.NumericUpDown();
            this.lblMaxNumber = new System.Windows.Forms.Label();
            this.numMagneticPoints = new System.Windows.Forms.NumericUpDown();
            this.lblMagneticPoints = new System.Windows.Forms.Label();
            this.numTwoCombinationMax = new System.Windows.Forms.NumericUpDown();
            this.numTwoCombinationMin = new System.Windows.Forms.NumericUpDown();
            this.lblTwoCombination = new System.Windows.Forms.Label();
            this.numResultMax = new System.Windows.Forms.NumericUpDown();
            this.numResultMin = new System.Windows.Forms.NumericUpDown();
            this.lblResultRange = new System.Windows.Forms.Label();
            this.groupBoxProcess = new System.Windows.Forms.GroupBox();
            this.btnStart = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblStatus = new System.Windows.Forms.Label();
            this.groupBoxResults = new System.Windows.Forms.GroupBox();
            this.txtResults = new System.Windows.Forms.TextBox();
            this.lblResultCount = new System.Windows.Forms.Label();
            this.btnExport = new System.Windows.Forms.Button();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.groupBoxFile.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).BeginInit();
            this.groupBoxProcess.SuspendLayout();
            this.groupBoxResults.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxFile
            // 
            this.groupBoxFile.Controls.Add(this.btnSelectFile);
            this.groupBoxFile.Controls.Add(this.txtFilePath);
            this.groupBoxFile.Controls.Add(this.lblFilePath);
            this.groupBoxFile.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxFile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxFile.Location = new System.Drawing.Point(20, 20);
            this.groupBoxFile.Name = "groupBoxFile";
            this.groupBoxFile.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxFile.Size = new System.Drawing.Size(800, 90);
            this.groupBoxFile.TabIndex = 0;
            this.groupBoxFile.TabStop = false;
            this.groupBoxFile.Text = "📁 数据文件";
            // 
            // btnSelectFile
            // 
            this.btnSelectFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnSelectFile.FlatAppearance.BorderSize = 0;
            this.btnSelectFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(102)))), ((int)(((byte)(184)))));
            this.btnSelectFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(151)))), ((int)(((byte)(234)))));
            this.btnSelectFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSelectFile.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSelectFile.ForeColor = System.Drawing.Color.White;
            this.btnSelectFile.Location = new System.Drawing.Point(680, 35);
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.Size = new System.Drawing.Size(100, 35);
            this.btnSelectFile.TabIndex = 2;
            this.btnSelectFile.Text = "选择文件";
            this.btnSelectFile.UseVisualStyleBackColor = false;
            this.btnSelectFile.Click += new System.EventHandler(this.btnSelectFile_Click);
            // 
            // txtFilePath
            // 
            this.txtFilePath.BackColor = System.Drawing.Color.White;
            this.txtFilePath.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtFilePath.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtFilePath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.txtFilePath.Location = new System.Drawing.Point(90, 40);
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(580, 23);
            this.txtFilePath.TabIndex = 1;
            // 
            // lblFilePath
            // 
            this.lblFilePath.AutoSize = true;
            this.lblFilePath.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblFilePath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblFilePath.Location = new System.Drawing.Point(20, 42);
            this.lblFilePath.Name = "lblFilePath";
            this.lblFilePath.Size = new System.Drawing.Size(59, 17);
            this.lblFilePath.TabIndex = 0;
            this.lblFilePath.Text = "文件路径:";
            // 
            // groupBoxSettings
            // 
            this.groupBoxSettings.Controls.Add(this.numMaxNumber);
            this.groupBoxSettings.Controls.Add(this.lblMaxNumber);
            this.groupBoxSettings.Controls.Add(this.numMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.lblMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.numTwoCombinationMax);
            this.groupBoxSettings.Controls.Add(this.numTwoCombinationMin);
            this.groupBoxSettings.Controls.Add(this.lblTwoCombination);
            this.groupBoxSettings.Controls.Add(this.numResultMax);
            this.groupBoxSettings.Controls.Add(this.numResultMin);
            this.groupBoxSettings.Controls.Add(this.lblResultRange);
            this.groupBoxSettings.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxSettings.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxSettings.Location = new System.Drawing.Point(20, 125);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxSettings.Size = new System.Drawing.Size(800, 140);
            this.groupBoxSettings.TabIndex = 1;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "⚙️ 参数设置";
            // 
            // numMaxNumber
            // 
            this.numMaxNumber.BackColor = System.Drawing.Color.White;
            this.numMaxNumber.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numMaxNumber.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numMaxNumber.Location = new System.Drawing.Point(140, 35);
            this.numMaxNumber.Maximum = new decimal(new int[] {
            37,
            0,
            0,
            0});
            this.numMaxNumber.Minimum = new decimal(new int[] {
            33,
            0,
            0,
            0});
            this.numMaxNumber.Name = "numMaxNumber";
            this.numMaxNumber.Size = new System.Drawing.Size(90, 23);
            this.numMaxNumber.TabIndex = 1;
            this.numMaxNumber.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numMaxNumber.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            // 
            // lblMaxNumber
            // 
            this.lblMaxNumber.AutoSize = true;
            this.lblMaxNumber.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMaxNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMaxNumber.Location = new System.Drawing.Point(20, 37);
            this.lblMaxNumber.Name = "lblMaxNumber";
            this.lblMaxNumber.Size = new System.Drawing.Size(100, 17);
            this.lblMaxNumber.TabIndex = 0;
            this.lblMaxNumber.Text = "最大数值(33-37):";
            // 
            // numMagneticPoints
            // 
            this.numMagneticPoints.BackColor = System.Drawing.Color.White;
            this.numMagneticPoints.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numMagneticPoints.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMagneticPoints.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numMagneticPoints.Location = new System.Drawing.Point(390, 35);
            this.numMagneticPoints.Maximum = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numMagneticPoints.Minimum = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numMagneticPoints.Name = "numMagneticPoints";
            this.numMagneticPoints.Size = new System.Drawing.Size(90, 23);
            this.numMagneticPoints.TabIndex = 3;
            this.numMagneticPoints.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numMagneticPoints.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // lblMagneticPoints
            // 
            this.lblMagneticPoints.AutoSize = true;
            this.lblMagneticPoints.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMagneticPoints.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMagneticPoints.Location = new System.Drawing.Point(270, 37);
            this.lblMagneticPoints.Name = "lblMagneticPoints";
            this.lblMagneticPoints.Size = new System.Drawing.Size(98, 17);
            this.lblMagneticPoints.TabIndex = 2;
            this.lblMagneticPoints.Text = "磁控点位数(6/9):";
            // 
            // numTwoCombinationMax
            // 
            this.numTwoCombinationMax.BackColor = System.Drawing.Color.White;
            this.numTwoCombinationMax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numTwoCombinationMax.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numTwoCombinationMax.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numTwoCombinationMax.Location = new System.Drawing.Point(230, 75);
            this.numTwoCombinationMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numTwoCombinationMax.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTwoCombinationMax.Name = "numTwoCombinationMax";
            this.numTwoCombinationMax.Size = new System.Drawing.Size(70, 23);
            this.numTwoCombinationMax.TabIndex = 6;
            this.numTwoCombinationMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numTwoCombinationMax.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // numTwoCombinationMin
            // 
            this.numTwoCombinationMin.BackColor = System.Drawing.Color.White;
            this.numTwoCombinationMin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numTwoCombinationMin.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numTwoCombinationMin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numTwoCombinationMin.Location = new System.Drawing.Point(140, 75);
            this.numTwoCombinationMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numTwoCombinationMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numTwoCombinationMin.Name = "numTwoCombinationMin";
            this.numTwoCombinationMin.Size = new System.Drawing.Size(70, 23);
            this.numTwoCombinationMin.TabIndex = 5;
            this.numTwoCombinationMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numTwoCombinationMin.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // lblTwoCombination
            // 
            this.lblTwoCombination.AutoSize = true;
            this.lblTwoCombination.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTwoCombination.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblTwoCombination.Location = new System.Drawing.Point(20, 77);
            this.lblTwoCombination.Name = "lblTwoCombination";
            this.lblTwoCombination.Size = new System.Drawing.Size(102, 17);
            this.lblTwoCombination.TabIndex = 4;
            this.lblTwoCombination.Text = "2数组合频次范围:";
            // 
            // numResultMax
            // 
            this.numResultMax.BackColor = System.Drawing.Color.White;
            this.numResultMax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numResultMax.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numResultMax.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numResultMax.Location = new System.Drawing.Point(610, 75);
            this.numResultMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMax.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMax.Name = "numResultMax";
            this.numResultMax.Size = new System.Drawing.Size(70, 23);
            this.numResultMax.TabIndex = 9;
            this.numResultMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numResultMax.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // numResultMin
            // 
            this.numResultMin.BackColor = System.Drawing.Color.White;
            this.numResultMin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numResultMin.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numResultMin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numResultMin.Location = new System.Drawing.Point(520, 75);
            this.numResultMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMin.Name = "numResultMin";
            this.numResultMin.Size = new System.Drawing.Size(70, 23);
            this.numResultMin.TabIndex = 8;
            this.numResultMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numResultMin.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // lblResultRange
            // 
            this.lblResultRange.AutoSize = true;
            this.lblResultRange.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblResultRange.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblResultRange.Location = new System.Drawing.Point(390, 77);
            this.lblResultRange.Name = "lblResultRange";
            this.lblResultRange.Size = new System.Drawing.Size(107, 17);
            this.lblResultRange.TabIndex = 7;
            this.lblResultRange.Text = "最终结果频次范围:";
            // 
            // groupBoxProcess
            // 
            this.groupBoxProcess.Controls.Add(this.btnStart);
            this.groupBoxProcess.Controls.Add(this.btnCancel);
            this.groupBoxProcess.Controls.Add(this.progressBar);
            this.groupBoxProcess.Controls.Add(this.lblStatus);
            this.groupBoxProcess.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxProcess.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxProcess.Location = new System.Drawing.Point(20, 280);
            this.groupBoxProcess.Name = "groupBoxProcess";
            this.groupBoxProcess.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxProcess.Size = new System.Drawing.Size(800, 120);
            this.groupBoxProcess.TabIndex = 2;
            this.groupBoxProcess.TabStop = false;
            this.groupBoxProcess.Text = "⚡ 处理进度";
            // 
            // btnStart
            // 
            this.btnStart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnStart.FlatAppearance.BorderSize = 0;
            this.btnStart.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(174)))), ((int)(((byte)(96)))));
            this.btnStart.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(231)))), ((int)(((byte)(128)))));
            this.btnStart.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStart.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStart.ForeColor = System.Drawing.Color.White;
            this.btnStart.Location = new System.Drawing.Point(20, 70);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(120, 40);
            this.btnStart.TabIndex = 0;
            this.btnStart.Text = "开始计算";
            this.btnStart.UseVisualStyleBackColor = false;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnCancel.Enabled = false;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(57)))), ((int)(((byte)(43)))));
            this.btnCancel.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(148)))), ((int)(((byte)(138)))));
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(160, 70);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 40);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消计算";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // progressBar
            // 
            this.progressBar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.progressBar.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.progressBar.Location = new System.Drawing.Point(20, 35);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(760, 25);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
            this.progressBar.TabIndex = 2;
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblStatus.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(127)))), ((int)(((byte)(140)))), ((int)(((byte)(141)))));
            this.lblStatus.Location = new System.Drawing.Point(286, 82);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(56, 17);
            this.lblStatus.TabIndex = 3;
            this.lblStatus.Text = "准备就绪";
            // 
            // groupBoxResults
            // 
            this.groupBoxResults.Controls.Add(this.txtResults);
            this.groupBoxResults.Controls.Add(this.lblResultCount);
            this.groupBoxResults.Controls.Add(this.btnExport);
            this.groupBoxResults.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxResults.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxResults.Location = new System.Drawing.Point(20, 415);
            this.groupBoxResults.Name = "groupBoxResults";
            this.groupBoxResults.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxResults.Size = new System.Drawing.Size(800, 280);
            this.groupBoxResults.TabIndex = 3;
            this.groupBoxResults.TabStop = false;
            this.groupBoxResults.Text = "📊 计算结果";
            // 
            // txtResults
            // 
            this.txtResults.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.txtResults.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtResults.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtResults.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.txtResults.Location = new System.Drawing.Point(20, 60);
            this.txtResults.Multiline = true;
            this.txtResults.Name = "txtResults";
            this.txtResults.ReadOnly = true;
            this.txtResults.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtResults.Size = new System.Drawing.Size(760, 200);
            this.txtResults.TabIndex = 2;
            // 
            // lblResultCount
            // 
            this.lblResultCount.AutoSize = true;
            this.lblResultCount.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblResultCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblResultCount.Location = new System.Drawing.Point(20, 30);
            this.lblResultCount.Name = "lblResultCount";
            this.lblResultCount.Size = new System.Drawing.Size(70, 17);
            this.lblResultCount.TabIndex = 0;
            this.lblResultCount.Text = "结果数量: 0";
            // 
            // btnExport
            // 
            this.btnExport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnExport.Enabled = false;
            this.btnExport.FlatAppearance.BorderSize = 0;
            this.btnExport.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(68)))), ((int)(((byte)(173)))));
            this.btnExport.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(187)))), ((int)(((byte)(143)))), ((int)(((byte)(206)))));
            this.btnExport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExport.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExport.ForeColor = System.Drawing.Color.White;
            this.btnExport.Location = new System.Drawing.Point(680, 25);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(100, 30);
            this.btnExport.TabIndex = 1;
            this.btnExport.Text = "导出结果";
            this.btnExport.UseVisualStyleBackColor = false;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // openFileDialog
            // 
            this.openFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.openFileDialog.Title = "选择数据文件";
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.saveFileDialog.Title = "保存结果文件";
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.ClientSize = new System.Drawing.Size(840, 720);
            this.Controls.Add(this.groupBoxResults);
            this.Controls.Add(this.groupBoxProcess);
            this.Controls.Add(this.groupBoxSettings);
            this.Controls.Add(this.groupBoxFile);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "离子镀膜磁控点分布运算系统";
            this.groupBoxFile.ResumeLayout(false);
            this.groupBoxFile.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTwoCombinationMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).EndInit();
            this.groupBoxProcess.ResumeLayout(false);
            this.groupBoxProcess.PerformLayout();
            this.groupBoxResults.ResumeLayout(false);
            this.groupBoxResults.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
    }
}


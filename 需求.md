离子镀膜磁控点分布运算

 

1、 总数据TXT文档约有100-500行原始字符串，如A1-A3-A5-A8-A9-A10-A15.....A31-A33或-A37(最大33到37之间待定，现以33为最大数举例。每一行字符串数量不定)，为简便暂定省去前缀字母 如下

 

3-5-7-8-14-17-20-21-25-29-32-33

1-3-7-9-11-13-15-17-19-25-27-31

2-3-8-10-14-17-24-26-27-31-32

 

2、 原始字符串严格按照从小到大排序且不重复，数与数之间用键盘九宫格上 - 隔开。总数据TXT文档导入程序系统首先验证原始字符串是否按照要求，否则在相对应的行列报错。如 1-5-7-9-10-11-11-15-28  报错

 

3、 磁控点是由6个不同的字符串控制，如7-9-12-19-25-33

 

 

4、 程序系统把每一行原始字符串取不同的2个数的所有不重复的排列组合生成出来，如原始字符串1-2-3-4-5-6，取不同的2个数所组成不重复的小字符串有15种，1-2，1-3，1-4 ，1-5， 1-6， 2-3， 2-4，2-5， 2-6， 3-4， 3-5， 3-6， 4-5， 4-6，5-6。其他行原始字符串以此类推排列组合。最后遍历每一个小字符串出现的次数，如出现一次的所有小字符串放入一个集合，出现两次的放入一个集合，其他以此类推。如图（此图为出现3次的4个数的小字符串，现要求2个数的小字符串）

![img](assets/wps1.png) 

 

5、 如选择出现5-50次的所有2个数的小字符串共计300组参与运算，最大数至33.如

1-3

8-9

12-20

9-33

.....

系统选取一个固定小字符串如1-3，在剩下的1-33中剔除1和3这2个字符，因磁控点是由6个不同的数控制，1和3已经占据2个位置，空余4个位置只能由剩下的31个数填补。如

1-3&2-4-5-6-7-8-9-10-11-12-13-14-15-16-17-18-19-20-21-22-23-24-25-26-27-28-29-30-31-32-33。

系统把1和3固定，剩余4个数位由2-4-5-6-7-8-9-10-11-12-13-14-15-16-17-18-19-20-21-22-23-

24-25-26-27-28-29-30-31-32-33排列组合。如1-2-3-4-5-6、1-3-5-9-10-23、1-3-9-25-26-29-33等等。等同于在31个数中取4个不同的数的所有排列组合，再与1和3组合成一个全新的6个数的字符串。同理其他2个数的小字符串也是如此运行排列计算。

 

 

6、 程序所生成的所有全新6个数的字符串再次遍历，按限制要求筛选出现10-15次的，输出存入程序系统。其中限制条件可调节，如10-13表示出现10-13次符合条件的，15-15表示仅出现15次的。

 

7、 因33个数取2个不同的数的排列组合有33*32/2=528种，参与运算的假设只有300种，剩下228种为无用数据。程序再把运算出来存入系统的6个数的字符串中，剔除包含228种任何其中一个无用的2个数的字符串，最终输出txt文档

 